variance accured columns after last update

Q2 
Test 1

FULLY_AVAILABLE_QUARTER 
DAYOFWEEK_TIME_STD_AVG 
DAYOFWEEK_PROC_STD_AVG 
TOTAL_BUSINESS_DAYS 
PERCENTAGE_OR_DAYS_IN_QTR 

Test 2

DAYOFWEEK_TIME_STD_AVG 
DAYOFWEEK_PROC_STD_AVG 
TOTAL_GAPS_AVG 
TOTAL_GAPS_MEDIAN 
TOTAL_GAPS_STD 
GAPS_2_PROC_DAYS_AVG 
GAPS_2_PROC_DAYS_MEDIAN 
GAPS_2_PROC_DAYS_STD 
GAPS_2_PROC_DAYS_CV 


Q1 
Test 1

DAYOFWEEK_TIME_STD_AVG 
DAYOFWEEK_PROC_STD_AVG 
TOTAL_GAPS_AVG 
TOTAL_GAPS_MEDIAN 
TOTAL_GAPS_STD 
TOTAL_GAPS_CV 
AVERAGE_TURNOVER_TIME_MINS 
MEDIAN_TURNOVER_TIME_MINS 
STD_TURNOVER_TIME_MINS 
GAPS_3_PLUS_PROC_DAYS_CV 
GAPS_4_PROC_DAYS_AVG 
GAPS_4_PROC_DAYS_MEDIAN 
GAPS_4_PROC_DAYS_STD
GAPS_4_PROC_DAYS_CV 
PERCENTILE_RANK 

Test 2

DAYOFWEEK_TIME_STD_AVG 
DAYOFWEEK_PROC_STD_AVG 
TOTAL_GAPS_AVG 
TOTAL_GAPS_MEDIAN 
TOTAL_GAPS_STD 
TOTAL_GAPS_CV 
GAPS_2_PROC_DAYS_AVG 
GAPS_2_PROC_DAYS_MEDIAN 
GAPS_2_PROC_DAYS_STD 
GAPS_2_PROC_DAYS_CV 
PERCENTILE_RANK 


-- Q2 

select date(dwload_ts) load_date, count(1) K from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR group by 1 order by load_date desc;

-- result

-- LOAD_DATE	K
-- null    	21277
-- 2025-05-30	6820
-- 2025-02-28	6533
-- 2024-11-29	6232
-- 2024-08-30	6018
-- 2024-05-31	5923
-- 2024-03-01	5720
-- 2023-11-26	5918
-- 2023-09-15	101557

select date(dwload_ts) load_date, count(1) K from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS group by 1 order by load_date desc;

-- result

-- LOAD_DATE	K
-- null    	21293
-- 2025-05-30	6820
-- 2025-02-28	6533
-- 2024-11-29	6232
-- 2024-08-30	6018
-- 2024-05-31	5923
-- 2024-03-01	5720
-- 2023-11-26	5918
-- 2023-09-15	101557


with cte_prd_data as (
select distinct account_id, system_serial_number, qtr, (NVL(total_operating_hours_mean,0)+NVL(total_operating_hours_std,0)+NVL(utilization_day_mean,0)+NVL(afternoon_hour_on_or_day_mean,0)+NVL(afternoon_hour_on_or_day_std,0)+NVL(or_duration_per_day_mean,0)+NVL(or_duration_per_day_std,0)+NVL(percentage_system_used_weekly_avg,0)+NVL(percentage_system_used_weekly_std,0)+NVL(hours_system_used_weekly_avg,0)+NVL(hours_system_used_weekly_std,0)+NVL(weekly_system_used_std_median,0)+NVL(dayofweek_time_std_avg,0)+NVL(dayofweek_proc_std_avg,0)+NVL(days_procs_startend_past_12,0)+NVL(total_business_days,0)+NVL(proc_days_0,0)+NVL(proc_days_1,0)+NVL(proc_days_2,0)+NVL(proc_days_3,0)+NVL(proc_days_4,0)+NVL(proc_days_5,0)+NVL(proc_days_6,0)+NVL(proc_days_7,0)+NVL(proc_days_8,0)+NVL(proc_days_9,0)+NVL(proc_days_10,0)+NVL(proc_days_11,0)+NVL(plus_proc_days_3_plus,0)+NVL(percentage_surgeon_days,0)+NVL(percentage_or_days_past_11,0)+NVL(percentage_or_days_past_2,0)+NVL(percentage_or_days_in_qtr,0)+NVL(total_gaps_avg,0)+NVL(total_gaps_median,0)+NVL(total_gaps_std,0)+NVL(total_gaps_cv,0)+NVL(gaps_2_proc_days_avg,0)+NVL(gaps_2_proc_days_median,0)+NVL(gaps_2_proc_days_std,0)+NVL(gaps_2_proc_days_cv,0)+NVL(average_turnover_time_mins,0)+NVL(median_turnover_time_mins,0)+NVL(std_turnover_time_mins,0)+NVL(gaps_3_plus_proc_days_cv,0)+NVL(gaps_3_proc_days_avg,0)+NVL(gaps_3_proc_days_median,0)+NVL(gaps_3_proc_days_std,0)+NVL(gaps_3_proc_days_cv,0)+NVL(gaps_4_proc_days_avg,0)+NVL(gaps_4_proc_days_median,0)+NVL(gaps_4_proc_days_std,0)+NVL(gaps_4_proc_days_cv,0)+NVL(gaps_5_proc_days_avg,0)+NVL(gaps_5_proc_days_median,0)+NVL(gaps_5_proc_days_std,0)+NVL(gaps_5_proc_days_cv,0)+NVL(gaps_6_proc_days_avg,0)+NVL(gaps_6_proc_days_median,0)+NVL(gaps_6_proc_days_std,0)+NVL(gaps_6_proc_days_cv,0)+NVL(proc_days_0_percentage,0)+NVL(proc_days_1_percentage,0)+NVL(proc_days_2_percentage,0)+NVL(proc_days_3_percentage,0)+NVL(proc_days_4_percentage,0)+NVL(proc_days_5_percentage,0)+NVL(proc_days_6_percentage,0)+NVL(proc_days_7_percentage,0)+NVL(proc_days_8_percentage,0)+NVL(proc_days_9_percentage,0)+NVL(proc_days_10_percentage,0)+NVL(proc_days_11_percentage,0)+NVL(plus_proc_days_3_plus_percentage,0)+NVL(multiple_case_days,0)+NVL(nonzero_case_days,0)+NVL(percentile_rank,0)) as tot_val
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR where date(dwload_ts) is null
)
-- ;
-------------------------------------------------------------------------------------------------------------------------  
,cte_sbx_data as (
select distinct account_id, system_serial_number, qtr, (NVL(total_operating_hours_mean,0)+NVL(total_operating_hours_std,0)+NVL(utilization_day_mean,0)+NVL(afternoon_hour_on_or_day_mean,0)+NVL(afternoon_hour_on_or_day_std,0)+NVL(or_duration_per_day_mean,0)+NVL(or_duration_per_day_std,0)+NVL(percentage_system_used_weekly_avg,0)+NVL(percentage_system_used_weekly_std,0)+NVL(hours_system_used_weekly_avg,0)+NVL(hours_system_used_weekly_std,0)+NVL(weekly_system_used_std_median,0)+NVL(dayofweek_time_std_avg,0)+NVL(dayofweek_proc_std_avg,0)+NVL(days_procs_startend_past_12,0)+NVL(total_business_days,0)+NVL(proc_days_0,0)+NVL(proc_days_1,0)+NVL(proc_days_2,0)+NVL(proc_days_3,0)+NVL(proc_days_4,0)+NVL(proc_days_5,0)+NVL(proc_days_6,0)+NVL(proc_days_7,0)+NVL(proc_days_8,0)+NVL(proc_days_9,0)+NVL(proc_days_10,0)+NVL(proc_days_11,0)+NVL(plus_proc_days_3_plus,0)+NVL(percentage_surgeon_days,0)+NVL(percentage_or_days_past_11,0)+NVL(percentage_or_days_past_2,0)+NVL(percentage_or_days_in_qtr,0)+NVL(total_gaps_avg,0)+NVL(total_gaps_median,0)+NVL(total_gaps_std,0)+NVL(total_gaps_cv,0)+NVL(gaps_2_proc_days_avg,0)+NVL(gaps_2_proc_days_median,0)+NVL(gaps_2_proc_days_std,0)+NVL(gaps_2_proc_days_cv,0)+NVL(average_turnover_time_mins,0)+NVL(median_turnover_time_mins,0)+NVL(std_turnover_time_mins,0)+NVL(gaps_3_plus_proc_days_cv,0)+NVL(gaps_3_proc_days_avg,0)+NVL(gaps_3_proc_days_median,0)+NVL(gaps_3_proc_days_std,0)+NVL(gaps_3_proc_days_cv,0)+NVL(gaps_4_proc_days_avg,0)+NVL(gaps_4_proc_days_median,0)+NVL(gaps_4_proc_days_std,0)+NVL(gaps_4_proc_days_cv,0)+NVL(gaps_5_proc_days_avg,0)+NVL(gaps_5_proc_days_median,0)+NVL(gaps_5_proc_days_std,0)+NVL(gaps_5_proc_days_cv,0)+NVL(gaps_6_proc_days_avg,0)+NVL(gaps_6_proc_days_median,0)+NVL(gaps_6_proc_days_std,0)+NVL(gaps_6_proc_days_cv,0)+NVL(proc_days_0_percentage,0)+NVL(proc_days_1_percentage,0)+NVL(proc_days_2_percentage,0)+NVL(proc_days_3_percentage,0)+NVL(proc_days_4_percentage,0)+NVL(proc_days_5_percentage,0)+NVL(proc_days_6_percentage,0)+NVL(proc_days_7_percentage,0)+NVL(proc_days_8_percentage,0)+NVL(proc_days_9_percentage,0)+NVL(proc_days_10_percentage,0)+NVL(proc_days_11_percentage,0)+NVL(plus_proc_days_3_plus_percentage,0)+NVL(multiple_case_days,0)+NVL(nonzero_case_days,0)+NVL(percentile_rank,0)) as tot_val
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS where date(dwload_ts) is null
)
select prd.account_id, prd.system_serial_number, prd.qtr, round(prd.tot_val, 2) prd_val, round(sbx.tot_val, 2) sbx_val, round((prd.tot_val-sbx.tot_val),2) as variance
from cte_prd_data prd
inner join cte_sbx_data sbx
on prd.account_id = sbx.account_id and prd.system_serial_number = sbx.system_serial_number and prd.qtr = sbx.qtr
where prd_val<>sbx_val and prd.qtr='20252'
order by abs(variance) desc
limit 15
;

-- result  6k rows has minor variance q2 data

-- example few rows 

-- ACCOUNT_ID	SYSTEM_SERIAL_NUMBER	QTR	PRD_VAL	SBX_VAL	VARIANCE
-- 15008	SQ0301	20252	1552.58	1535.13	17.44
-- 20335	SK2921	20252	1109.76	1096.16	13.61
-- 14833	SK6420	20252	1659.37	1654.29	5.08
-- 10101	SK4781	20252	2054.36	2059.24	-4.88
-- 10975	SK6924	20252	1513.18	1517.93	-4.75
-- 10876	SK6122	20252	1542.77	1547.32	-4.55
-- 14831	SK4751	20252	1142.85	1147.17	-4.33
-- 11331	SQ0281	20252	1779.23	1783.52	-4.29
-- 12803	SK1732	20252	1254.83	1259.02	-4.19
-- 10204	SK0807	20252	2032.36	2036.5	-4.15
-- 10101	SK0445	20252	1956.84	1960.92	-4.08
-- 10204	SK0152	20252	2200.56	2204.61	-4.05
-- 20500	SK8147	20252	2383.96	2388.02	-4.05
-- 12734	SK2008	20252	2073.14	2077.18	-4.03
-- 12803	SK5348	20252	1316.64	1320.62	-3.98


select 'sbx' source, * from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
where date(dwload_ts) is null and account_id='15008' and system_serial_number='SQ0301' and qtr='20252'
UNION ALL
select 'prd' source, * from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
where date(dwload_ts) is null and account_id='15008' and system_serial_number='SQ0301' and qtr='20252'
;

-- result:  

-- test 1

-- SOURCE	SYSTEM_SERIAL_NUMBER	QTR	ACCOUNT_NAME	ACCOUNT_ID	HOSPITAL_GUID	MODEL	INSTALL_DATE	INACTIVE_FLAG	TYPE	REMOVE_DATE	COUNTRY	PROCS_PAST_12	TOTAL_OR_DAYS	AVAILABLE_BUSINESS_DAYS	FULLY_AVAILABLE_QUARTER	TOTAL_OPERATING_HOURS_MEAN	TOTAL_OPERATING_HOURS_STD	UTILIZATION_DAY_MEAN	AFTERNOON_HOUR_ON_OR_DAY_MEAN	AFTERNOON_HOUR_ON_OR_DAY_STD	OR_DURATION_PER_DAY_MEAN	OR_DURATION_PER_DAY_STD	PERCENTAGE_SYSTEM_USED_WEEKLY_AVG	PERCENTAGE_SYSTEM_USED_WEEKLY_STD	HOURS_SYSTEM_USED_WEEKLY_AVG	HOURS_SYSTEM_USED_WEEKLY_STD	WEEKLY_SYSTEM_USED_STD_MEDIAN	DAYOFWEEK_TIME_STD_AVG	DAYOFWEEK_PROC_STD_AVG	DAYS_PROCS_STARTEND_PAST_12	TOTAL_BUSINESS_DAYS	PROC_DAYS_0	PROC_DAYS_1	PROC_DAYS_2	PROC_DAYS_3	PROC_DAYS_4	PROC_DAYS_5	PROC_DAYS_6	PROC_DAYS_7	PROC_DAYS_8	PROC_DAYS_9	PROC_DAYS_10	PROC_DAYS_11	PLUS_PROC_DAYS_3_PLUS	PERCENTAGE_SURGEON_DAYS	PERCENTAGE_OR_DAYS_PAST_11	PERCENTAGE_OR_DAYS_PAST_2	PERCENTAGE_OR_DAYS_IN_QTR	TOTAL_GAPS_AVG	TOTAL_GAPS_MEDIAN	TOTAL_GAPS_STD	TOTAL_GAPS_CV	GAPS_2_PROC_DAYS_AVG	GAPS_2_PROC_DAYS_MEDIAN	GAPS_2_PROC_DAYS_STD	GAPS_2_PROC_DAYS_CV	AVERAGE_TURNOVER_TIME_MINS	MEDIAN_TURNOVER_TIME_MINS	STD_TURNOVER_TIME_MINS	GAPS_3_PLUS_PROC_DAYS_CV	GAPS_3_PROC_DAYS_AVG	GAPS_3_PROC_DAYS_MEDIAN	GAPS_3_PROC_DAYS_STD	GAPS_3_PROC_DAYS_CV	GAPS_4_PROC_DAYS_AVG	GAPS_4_PROC_DAYS_MEDIAN	GAPS_4_PROC_DAYS_STD	GAPS_4_PROC_DAYS_CV	GAPS_5_PROC_DAYS_AVG	GAPS_5_PROC_DAYS_MEDIAN	GAPS_5_PROC_DAYS_STD	GAPS_5_PROC_DAYS_CV	GAPS_6_PROC_DAYS_AVG	GAPS_6_PROC_DAYS_MEDIAN	GAPS_6_PROC_DAYS_STD	GAPS_6_PROC_DAYS_CV	PROC_DAYS_0_PERCENTAGE	PROC_DAYS_1_PERCENTAGE	PROC_DAYS_2_PERCENTAGE	PROC_DAYS_3_PERCENTAGE	PROC_DAYS_4_PERCENTAGE	PROC_DAYS_5_PERCENTAGE	PROC_DAYS_6_PERCENTAGE	PROC_DAYS_7_PERCENTAGE	PROC_DAYS_8_PERCENTAGE	PROC_DAYS_9_PERCENTAGE	PROC_DAYS_10_PERCENTAGE	PROC_DAYS_11_PERCENTAGE	PLUS_PROC_DAYS_3_PLUS_PERCENTAGE	MULTIPLE_CASE_DAYS	NONZERO_CASE_DAYS	AVERAGE_START_TIME	AVERAGE_END_TIME	PERCENTILE_RANK	CSM_NAME	CSD_NAME	CVP_NAME	CSR_NAME	GENESIS_TRAINER	DWLOAD_TS
-- sbx	SQ0301	20252	Methodist Hospital	15008	001o000000iySzIAAU	da Vinci 5	2025-04-29	F	System		United States		27	65	FALSE	6.356666667	2.054419252	0.7988302964	2.950617284	1.152231962	4.943197531	1.464754524	0.2383327381	0.240790719	9.533309524	9.631628761		1.320783713	0.5634101228	0	45	38	6	18	3	0	0	0	0	0	0	0	0	3	1	0.851852	0.259259	0.6	145.417	140.5	31.525	0.217	149.167	148	26.359	0.177	134.167	124	41.499	0.309	134.167	124	41.499	0.309													0.571429	0.095238	0.285714	0.047619									0.047619	21	27	1900-01-01 08:52:55.000	1900-01-01 15:14:22.000	87.*********	Jim Rego	Jim Barbrack	Mark McSweeney	Jack Rosenfeld		
-- prd	SQ0301	20252	Methodist Hospital	15008	001o000000iySzIAAU	da Vinci 5	2024-11-06	F	System		United States		27	65	TRUE	6.356666667	2.054419252	0.7988302964	2.950617284	1.152231962	4.943197531	1.464754524	0.2383327381	0.240790719	9.533309524	9.631628761		1.003501618	0.**********	0	63	38	6	18	3	0	0	0	0	0	0	0	0	3	1	0.851852	0.259259	0.428571	145.417	140.5	31.525	0.217	149.167	148	26.359	0.177	134.167	124	41.499	0.309	134.167	124	41.499	0.309													0.**********	0.***********	0.**********	0.***********									0.***********	21	27	1900-01-01 08:52:55.000	1900-01-01 15:14:22.000	87.*********	Jim Rego	Jim Barbrack	Mark McSweeney	Jack Rosenfeld								

select 'sbx' source, * from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
where date(dwload_ts) is null and account_id='20335' and system_serial_number='SK2921' and qtr='20252'
UNION ALL
select 'prd' source, * from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
where date(dwload_ts) is null and account_id='20335' and system_serial_number='SK2921' and qtr='20252'
;
-- test 2

-- SOURCE	SYSTEM_SERIAL_NUMBER	QTR	ACCOUNT_NAME	ACCOUNT_ID	HOSPITAL_GUID	MODEL	INSTALL_DATE	INACTIVE_FLAG	TYPE	REMOVE_DATE	COUNTRY	PROCS_PAST_12	TOTAL_OR_DAYS	AVAILABLE_BUSINESS_DAYS	FULLY_AVAILABLE_QUARTER	TOTAL_OPERATING_HOURS_MEAN	TOTAL_OPERATING_HOURS_STD	UTILIZATION_DAY_MEAN	AFTERNOON_HOUR_ON_OR_DAY_MEAN	AFTERNOON_HOUR_ON_OR_DAY_STD	OR_DURATION_PER_DAY_MEAN	OR_DURATION_PER_DAY_STD	PERCENTAGE_SYSTEM_USED_WEEKLY_AVG	PERCENTAGE_SYSTEM_USED_WEEKLY_STD	HOURS_SYSTEM_USED_WEEKLY_AVG	HOURS_SYSTEM_USED_WEEKLY_STD	WEEKLY_SYSTEM_USED_STD_MEDIAN	DAYOFWEEK_TIME_STD_AVG	DAYOFWEEK_PROC_STD_AVG	DAYS_PROCS_STARTEND_PAST_12	TOTAL_BUSINESS_DAYS	PROC_DAYS_0	PROC_DAYS_1	PROC_DAYS_2	PROC_DAYS_3	PROC_DAYS_4	PROC_DAYS_5	PROC_DAYS_6	PROC_DAYS_7	PROC_DAYS_8	PROC_DAYS_9	PROC_DAYS_10	PROC_DAYS_11	PLUS_PROC_DAYS_3_PLUS	PERCENTAGE_SURGEON_DAYS	PERCENTAGE_OR_DAYS_PAST_11	PERCENTAGE_OR_DAYS_PAST_2	PERCENTAGE_OR_DAYS_IN_QTR	TOTAL_GAPS_AVG	TOTAL_GAPS_MEDIAN	TOTAL_GAPS_STD	TOTAL_GAPS_CV	GAPS_2_PROC_DAYS_AVG	GAPS_2_PROC_DAYS_MEDIAN	GAPS_2_PROC_DAYS_STD	GAPS_2_PROC_DAYS_CV	AVERAGE_TURNOVER_TIME_MINS	MEDIAN_TURNOVER_TIME_MINS	STD_TURNOVER_TIME_MINS	GAPS_3_PLUS_PROC_DAYS_CV	GAPS_3_PROC_DAYS_AVG	GAPS_3_PROC_DAYS_MEDIAN	GAPS_3_PROC_DAYS_STD	GAPS_3_PROC_DAYS_CV	GAPS_4_PROC_DAYS_AVG	GAPS_4_PROC_DAYS_MEDIAN	GAPS_4_PROC_DAYS_STD	GAPS_4_PROC_DAYS_CV	GAPS_5_PROC_DAYS_AVG	GAPS_5_PROC_DAYS_MEDIAN	GAPS_5_PROC_DAYS_STD	GAPS_5_PROC_DAYS_CV	GAPS_6_PROC_DAYS_AVG	GAPS_6_PROC_DAYS_MEDIAN	GAPS_6_PROC_DAYS_STD	GAPS_6_PROC_DAYS_CV	PROC_DAYS_0_PERCENTAGE	PROC_DAYS_1_PERCENTAGE	PROC_DAYS_2_PERCENTAGE	PROC_DAYS_3_PERCENTAGE	PROC_DAYS_4_PERCENTAGE	PROC_DAYS_5_PERCENTAGE	PROC_DAYS_6_PERCENTAGE	PROC_DAYS_7_PERCENTAGE	PROC_DAYS_8_PERCENTAGE	PROC_DAYS_9_PERCENTAGE	PROC_DAYS_10_PERCENTAGE	PROC_DAYS_11_PERCENTAGE	PLUS_PROC_DAYS_3_PLUS_PERCENTAGE	MULTIPLE_CASE_DAYS	NONZERO_CASE_DAYS	AVERAGE_START_TIME	AVERAGE_END_TIME	PERCENTILE_RANK	CSM_NAME	CSD_NAME	CVP_NAME	CSR_NAME	GENESIS_TRAINER	DWLOAD_TS
-- sbx	SK2921	20252	Mercy Health - Defiance Hospital - OH	20335	001o000000iySyUAAU	da Vinci Xi	2019-09-10	F	System		United States		30	65	TRUE	4.***********.***********.**********	2.132777778	1.***********.107722222	2.153811311	0.**********	0.09156772253	8.802261905	3.662708901		1.***********.**********	0	63	35	15	6	8	1	0	0	0	0	0	0	0	9	0.966667	0.6	0.2	0.47619	83.385	66	53.352	0.64	89.333	73.5	52.36	0.586	73.105	64	39.643	0.542	79.125	65.5	40.401	0.511	41	41	4.899	0.119									0.52381	0.238095	0.095238	0.126984	0.015873								0.142857	15	30	1900-01-01 09:22:34.000	1900-01-01 13:51:34.000	8.995107264	Matthew Hammons	Matt Pierce	Andrea Crist	CTA Coverage Defiance OH		
-- prd	SK2921	20252	Mercy Health - Defiance Hospital - OH	20335	001o000000iySyUAAU	da Vinci Xi	2019-09-10	F	System		United States		30	65	TRUE	4.***********.***********.**********	2.132777778	1.***********.107722222	2.153811311	0.**********	0.09156772253	8.802261905	3.662708901		1.***********.**********	0	63	35	15	6	8	1	0	0	0	0	0	0	0	9	0.966667	0.6	0.2	0.47619	84.12	67	54.279	0.645	94.2	76	56.105	0.596	73.105	64	39.643	0.542	79.125	65.5	40.401	0.511	41	41	4.899	0.119									0.**********	0.**********	0.***********	0.126984127	0.01587301587								0.**********	15	30	1900-01-01 09:22:34.000	1900-01-01 13:51:34.000	8.995107264	Matthew Hammons	Matt Pierce	Andrea Crist	CTA Coverage Defiance OH							


-- comparing 2025-Q2 data which is fine without any variance as seen above results, we need to check for Q1 data as well.
-- since 2025-Q2 is completed, there should not be any gap between prod and SBX even if the load date is different,
-- to check for individual case
-- using above query
-- if we find any gaps because of load time gap for 2025-Q2, we can take 2025-Q1 data for validation


-- Q1 data validation

-- Production data load dates
select date(dwload_ts) load_date, count(1) K 
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
group by 1 order by load_date desc;

-- result

-- LOAD_DATE	K
-- null    	21277
-- 2025-05-30	6820
-- 2025-02-28	6533
-- 2024-11-29	6232
-- 2024-08-30	6018
-- 2024-05-31	5923
-- 2024-03-01	5720
-- 2023-11-26	5918
-- 2023-09-15	101557



-- Sandbox data load dates
select date(dwload_ts) load_date, count(1) K 
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
group by 1 order by load_date desc;

-- result

-- LOAD_DATE	K
-- null    	21298
-- 2025-05-30	6820
-- 2025-02-28	6533
-- 2024-11-29	6232
-- 2024-08-30	6018
-- 2024-05-31	5923
-- 2024-03-01	5720
-- 2023-11-26	5918
-- 2023-09-15	101557


with cte_prd_data as (
select distinct account_id, system_serial_number, qtr, 
(NVL(total_operating_hours_mean,0)+NVL(total_operating_hours_std,0)+NVL(utilization_day_mean,0)+NVL(afternoon_hour_on_or_day_mean,0)+NVL(afternoon_hour_on_or_day_std,0)+NVL(or_duration_per_day_mean,0)+NVL(or_duration_per_day_std,0)+NVL(percentage_system_used_weekly_avg,0)+NVL(percentage_system_used_weekly_std,0)+NVL(hours_system_used_weekly_avg,0)+NVL(hours_system_used_weekly_std,0)+NVL(weekly_system_used_std_median,0)+NVL(dayofweek_time_std_avg,0)+NVL(dayofweek_proc_std_avg,0)+NVL(days_procs_startend_past_12,0)+NVL(total_business_days,0)+NVL(proc_days_0,0)+NVL(proc_days_1,0)+NVL(proc_days_2,0)+NVL(proc_days_3,0)+NVL(proc_days_4,0)+NVL(proc_days_5,0)+NVL(proc_days_6,0)+NVL(proc_days_7,0)+NVL(proc_days_8,0)+NVL(proc_days_9,0)+NVL(proc_days_10,0)+NVL(proc_days_11,0)+NVL(plus_proc_days_3_plus,0)+NVL(percentage_surgeon_days,0)+NVL(percentage_or_days_past_11,0)+NVL(percentage_or_days_past_2,0)+NVL(percentage_or_days_in_qtr,0)+NVL(total_gaps_avg,0)+NVL(total_gaps_median,0)+NVL(total_gaps_std,0)+NVL(total_gaps_cv,0)+NVL(gaps_2_proc_days_avg,0)+NVL(gaps_2_proc_days_median,0)+NVL(gaps_2_proc_days_std,0)+NVL(gaps_2_proc_days_cv,0)+NVL(average_turnover_time_mins,0)+NVL(median_turnover_time_mins,0)+NVL(std_turnover_time_mins,0)+NVL(gaps_3_plus_proc_days_cv,0)+NVL(gaps_3_proc_days_avg,0)+NVL(gaps_3_proc_days_median,0)+NVL(gaps_3_proc_days_std,0)+NVL(gaps_3_proc_days_cv,0)+NVL(gaps_4_proc_days_avg,0)+NVL(gaps_4_proc_days_median,0)+NVL(gaps_4_proc_days_std,0)+NVL(gaps_4_proc_days_cv,0)+NVL(gaps_5_proc_days_avg,0)+NVL(gaps_5_proc_days_median,0)+NVL(gaps_5_proc_days_std,0)+NVL(gaps_5_proc_days_cv,0)+NVL(gaps_6_proc_days_avg,0)+NVL(gaps_6_proc_days_median,0)+NVL(gaps_6_proc_days_std,0)+NVL(gaps_6_proc_days_cv,0)+NVL(proc_days_0_percentage,0)+NVL(proc_days_1_percentage,0)+NVL(proc_days_2_percentage,0)+NVL(proc_days_3_percentage,0)+NVL(proc_days_4_percentage,0)+NVL(proc_days_5_percentage,0)+NVL(proc_days_6_percentage,0)+NVL(proc_days_7_percentage,0)+NVL(proc_days_8_percentage,0)+NVL(proc_days_9_percentage,0)+NVL(proc_days_10_percentage,0)+NVL(proc_days_11_percentage,0)+NVL(plus_proc_days_3_plus_percentage,0)+NVL(multiple_case_days,0)+NVL(nonzero_case_days,0)+NVL(percentile_rank,0)) as tot_val
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
where qtr = '20251'  -- Q1 2025
)
,cte_sbx_data as (
select distinct account_id, system_serial_number, qtr, 
(NVL(total_operating_hours_mean,0)+NVL(total_operating_hours_std,0)+NVL(utilization_day_mean,0)+NVL(afternoon_hour_on_or_day_mean,0)+NVL(afternoon_hour_on_or_day_std,0)+NVL(or_duration_per_day_mean,0)+NVL(or_duration_per_day_std,0)+NVL(percentage_system_used_weekly_avg,0)+NVL(percentage_system_used_weekly_std,0)+NVL(hours_system_used_weekly_avg,0)+NVL(hours_system_used_weekly_std,0)+NVL(weekly_system_used_std_median,0)+NVL(dayofweek_time_std_avg,0)+NVL(dayofweek_proc_std_avg,0)+NVL(days_procs_startend_past_12,0)+NVL(total_business_days,0)+NVL(proc_days_0,0)+NVL(proc_days_1,0)+NVL(proc_days_2,0)+NVL(proc_days_3,0)+NVL(proc_days_4,0)+NVL(proc_days_5,0)+NVL(proc_days_6,0)+NVL(proc_days_7,0)+NVL(proc_days_8,0)+NVL(proc_days_9,0)+NVL(proc_days_10,0)+NVL(proc_days_11,0)+NVL(plus_proc_days_3_plus,0)+NVL(percentage_surgeon_days,0)+NVL(percentage_or_days_past_11,0)+NVL(percentage_or_days_past_2,0)+NVL(percentage_or_days_in_qtr,0)+NVL(total_gaps_avg,0)+NVL(total_gaps_median,0)+NVL(total_gaps_std,0)+NVL(total_gaps_cv,0)+NVL(gaps_2_proc_days_avg,0)+NVL(gaps_2_proc_days_median,0)+NVL(gaps_2_proc_days_std,0)+NVL(gaps_2_proc_days_cv,0)+NVL(average_turnover_time_mins,0)+NVL(median_turnover_time_mins,0)+NVL(std_turnover_time_mins,0)+NVL(gaps_3_plus_proc_days_cv,0)+NVL(gaps_3_proc_days_avg,0)+NVL(gaps_3_proc_days_median,0)+NVL(gaps_3_proc_days_std,0)+NVL(gaps_3_proc_days_cv,0)+NVL(gaps_4_proc_days_avg,0)+NVL(gaps_4_proc_days_median,0)+NVL(gaps_4_proc_days_std,0)+NVL(gaps_4_proc_days_cv,0)+NVL(gaps_5_proc_days_avg,0)+NVL(gaps_5_proc_days_median,0)+NVL(gaps_5_proc_days_std,0)+NVL(gaps_5_proc_days_cv,0)+NVL(gaps_6_proc_days_avg,0)+NVL(gaps_6_proc_days_median,0)+NVL(gaps_6_proc_days_std,0)+NVL(gaps_6_proc_days_cv,0)+NVL(proc_days_0_percentage,0)+NVL(proc_days_1_percentage,0)+NVL(proc_days_2_percentage,0)+NVL(proc_days_3_percentage,0)+NVL(proc_days_4_percentage,0)+NVL(proc_days_5_percentage,0)+NVL(proc_days_6_percentage,0)+NVL(proc_days_7_percentage,0)+NVL(proc_days_8_percentage,0)+NVL(proc_days_9_percentage,0)+NVL(proc_days_10_percentage,0)+NVL(proc_days_11_percentage,0)+NVL(plus_proc_days_3_plus_percentage,0)+NVL(multiple_case_days,0)+NVL(nonzero_case_days,0)+NVL(percentile_rank,0)) as tot_val
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
where qtr = '20251'  -- Q1 2025
)
select prd.account_id, prd.system_serial_number, prd.qtr, 
       round(prd.tot_val, 2) prd_val, 
       round(sbx.tot_val, 2) sbx_val, 
       round((prd.tot_val-sbx.tot_val),2) as variance
from cte_prd_data prd
inner join cte_sbx_data sbx
  on prd.account_id = sbx.account_id 
  and prd.system_serial_number = sbx.system_serial_number 
  and prd.qtr = sbx.qtr
where prd_val <> sbx_val 
  and prd.qtr = '20251'  -- Q1 2025
order by abs(variance) desc
limit 15
;

-- Result

-- 5.8k rows

-- sample rows from result

-- ACCOUNT_ID	SYSTEM_SERIAL_NUMBER	QTR	PRD_VAL	SBX_VAL	VARIANCE
-- 10639	SK6779	20251	1509.79	1521.33	-11.54
-- 15886	SQ0082	20251	1577.42	1568.42	9.01
-- 119729	SK8026	20251	1488.3	1495.07	-6.78
-- 10204	SK0807	20251	2064.69	2069.26	-4.57
-- 10101	SK0445	20251	2086.5	2090.82	-4.32
-- 10101	SK4781	20251	2402.78	2407.08	-4.3
-- 10146	SQ0276	20251	1820.81	1825.07	-4.25
-- 11007	SK5159	20251	2020.1	2024.32	-4.22
-- 10876	SK6122	20251	1624.76	1628.93	-4.17
-- 10204	SK0152	20251	2292.1	2296.26	-4.16
-- 16864	SK7120	20251	1573.04	1577.18	-4.14
-- 10975	SK6924	20251	1471.64	1475.75	-4.12
-- 12803	SK5348	20251	1400.39	1404.46	-4.07
-- 10158	SK6347	20251	2079.55	2083.62	-4.07
-- 11601	SK1519	20251	1898.67	1902.71	-4.04



-- Replace with actual account_id and system_serial_number from Q1 2025 data
select 'sbx' source, * 
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
where account_id = '10639'  
  and system_serial_number = 'SK6779'  
  and qtr = '20251'
UNION ALL
select 'prd' source, * 
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
where account_id = '10639'  -- Replace with actual value
  and system_serial_number = 'SK6779'  -- Replace with actual value
  and qtr = '20251';

  select 'sbx' source, * 
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
where account_id = '15886'  
  and system_serial_number = 'SQ0082' 
  and qtr = '20251'
UNION ALL
select 'prd' source, * 
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
where account_id = '15886'  -- Replace with actual value
  and system_serial_number = 'SQ0082'  -- Replace with actual value
  and qtr = '20251';


  -- results 

  -- test 1

-- SOURCE	SYSTEM_SERIAL_NUMBER	QTR	ACCOUNT_NAME	ACCOUNT_ID	HOSPITAL_GUID	MODEL	INSTALL_DATE	INACTIVE_FLAG	TYPE	REMOVE_DATE	COUNTRY	PROCS_PAST_12	TOTAL_OR_DAYS	AVAILABLE_BUSINESS_DAYS	FULLY_AVAILABLE_QUARTER	TOTAL_OPERATING_HOURS_MEAN	TOTAL_OPERATING_HOURS_STD	UTILIZATION_DAY_MEAN	AFTERNOON_HOUR_ON_OR_DAY_MEAN	AFTERNOON_HOUR_ON_OR_DAY_STD	OR_DURATION_PER_DAY_MEAN	OR_DURATION_PER_DAY_STD	PERCENTAGE_SYSTEM_USED_WEEKLY_AVG	PERCENTAGE_SYSTEM_USED_WEEKLY_STD	HOURS_SYSTEM_USED_WEEKLY_AVG	HOURS_SYSTEM_USED_WEEKLY_STD	WEEKLY_SYSTEM_USED_STD_MEDIAN	DAYOFWEEK_TIME_STD_AVG	DAYOFWEEK_PROC_STD_AVG	DAYS_PROCS_STARTEND_PAST_12	TOTAL_BUSINESS_DAYS	PROC_DAYS_0	PROC_DAYS_1	PROC_DAYS_2	PROC_DAYS_3	PROC_DAYS_4	PROC_DAYS_5	PROC_DAYS_6	PROC_DAYS_7	PROC_DAYS_8	PROC_DAYS_9	PROC_DAYS_10	PROC_DAYS_11	PLUS_PROC_DAYS_3_PLUS	PERCENTAGE_SURGEON_DAYS	PERCENTAGE_OR_DAYS_PAST_11	PERCENTAGE_OR_DAYS_PAST_2	PERCENTAGE_OR_DAYS_IN_QTR	TOTAL_GAPS_AVG	TOTAL_GAPS_MEDIAN	TOTAL_GAPS_STD	TOTAL_GAPS_CV	GAPS_2_PROC_DAYS_AVG	GAPS_2_PROC_DAYS_MEDIAN	GAPS_2_PROC_DAYS_STD	GAPS_2_PROC_DAYS_CV	AVERAGE_TURNOVER_TIME_MINS	MEDIAN_TURNOVER_TIME_MINS	STD_TURNOVER_TIME_MINS	GAPS_3_PLUS_PROC_DAYS_CV	GAPS_3_PROC_DAYS_AVG	GAPS_3_PROC_DAYS_MEDIAN	GAPS_3_PROC_DAYS_STD	GAPS_3_PROC_DAYS_CV	GAPS_4_PROC_DAYS_AVG	GAPS_4_PROC_DAYS_MEDIAN	GAPS_4_PROC_DAYS_STD	GAPS_4_PROC_DAYS_CV	GAPS_5_PROC_DAYS_AVG	GAPS_5_PROC_DAYS_MEDIAN	GAPS_5_PROC_DAYS_STD	GAPS_5_PROC_DAYS_CV	GAPS_6_PROC_DAYS_AVG	GAPS_6_PROC_DAYS_MEDIAN	GAPS_6_PROC_DAYS_STD	GAPS_6_PROC_DAYS_CV	PROC_DAYS_0_PERCENTAGE	PROC_DAYS_1_PERCENTAGE	PROC_DAYS_2_PERCENTAGE	PROC_DAYS_3_PERCENTAGE	PROC_DAYS_4_PERCENTAGE	PROC_DAYS_5_PERCENTAGE	PROC_DAYS_6_PERCENTAGE	PROC_DAYS_7_PERCENTAGE	PROC_DAYS_8_PERCENTAGE	PROC_DAYS_9_PERCENTAGE	PROC_DAYS_10_PERCENTAGE	PROC_DAYS_11_PERCENTAGE	PLUS_PROC_DAYS_3_PLUS_PERCENTAGE	MULTIPLE_CASE_DAYS	NONZERO_CASE_DAYS	AVERAGE_START_TIME	AVERAGE_END_TIME	PERCENTILE_RANK	CSM_NAME	CSD_NAME	CVP_NAME	CSR_NAME	GENESIS_TRAINER	DWLOAD_TS
-- prd	SK6779	20251	Providence Saint Joseph Medical Center	10639	001o000000iyScmAAE	da Vinci Xi	2023-06-20	F	System		United States		34	61	TRUE	6.*********	3.********	0.**********	3.932352941	2.46921429	5.487098039	2.931535741	0.**********	0.**********	13.325809524	6.717290152		2.244415752	0.**********	0	61	27	16	10	3	4	1	0	0	0	0	0	0	8	0.882353	0.852941	0.676471	0.557377	99.452	86	51.161	0.514	136.2	107.5	69.742	0.512	81.952	77	24.461	0.298	73.333	70.5	19.006	0.259	90.455	81	27.151	0.3	71.5	71	12.359	0.173					0.**********	0.262295082	0.**********	0.04918032787	0.06557377049	0.0**********							0.131147541	18	34	1900-01-01 10:58:21.000	1900-01-01 17:14:31.000	22.571872572	Lauren Albientz	Nick Mitchell	Scott Floyd	Open Los Angeles CSR 4	Matt Johnson	
-- sbx	SK6779	20251	Providence Saint Joseph Medical Center	10639	001o000000iyScmAAE	da Vinci Xi	2023-06-20	F	System		United States		34	61	TRUE	6.*********	3.********	0.**********	3.932352941	2.46921429	5.487098039	2.931535741	0.**********	0.**********	13.325809524	6.717290152		2.*********	0.**********	0	61	27	16	10	3	4	1	0	0	0	0	0	0	8	0.882353	0.852941	0.676471	0.557377	99.844	87	50.402	0.505	136.2	107.5	69.742	0.512	83.318	79	24.704	0.297	73.333	70.5	19.006	0.259	92.25	84.5	26.668	0.289	71.5	71	12.359	0.173					0.442623	0.262295	0.163934	0.04918	0.065574	0.016393							0.131148	18	34	1900-01-01 10:58:21.000	1900-01-01 17:14:31.000	24.*********	Lauren Albientz	Nick Mitchell	Scott Floyd	Open Los Angeles CSR 4	Matt Johnson						

-- test 2

-- SOURCE	SYSTEM_SERIAL_NUMBER	QTR	ACCOUNT_NAME	ACCOUNT_ID	HOSPITAL_GUID	MODEL	INSTALL_DATE	INACTIVE_FLAG	TYPE	REMOVE_DATE	COUNTRY	PROCS_PAST_12	TOTAL_OR_DAYS	AVAILABLE_BUSINESS_DAYS	FULLY_AVAILABLE_QUARTER	TOTAL_OPERATING_HOURS_MEAN	TOTAL_OPERATING_HOURS_STD	UTILIZATION_DAY_MEAN	AFTERNOON_HOUR_ON_OR_DAY_MEAN	AFTERNOON_HOUR_ON_OR_DAY_STD	OR_DURATION_PER_DAY_MEAN	OR_DURATION_PER_DAY_STD	PERCENTAGE_SYSTEM_USED_WEEKLY_AVG	PERCENTAGE_SYSTEM_USED_WEEKLY_STD	HOURS_SYSTEM_USED_WEEKLY_AVG	HOURS_SYSTEM_USED_WEEKLY_STD	WEEKLY_SYSTEM_USED_STD_MEDIAN	DAYOFWEEK_TIME_STD_AVG	DAYOFWEEK_PROC_STD_AVG	DAYS_PROCS_STARTEND_PAST_12	TOTAL_BUSINESS_DAYS	PROC_DAYS_0	PROC_DAYS_1	PROC_DAYS_2	PROC_DAYS_3	PROC_DAYS_4	PROC_DAYS_5	PROC_DAYS_6	PROC_DAYS_7	PROC_DAYS_8	PROC_DAYS_9	PROC_DAYS_10	PROC_DAYS_11	PLUS_PROC_DAYS_3_PLUS	PERCENTAGE_SURGEON_DAYS	PERCENTAGE_OR_DAYS_PAST_11	PERCENTAGE_OR_DAYS_PAST_2	PERCENTAGE_OR_DAYS_IN_QTR	TOTAL_GAPS_AVG	TOTAL_GAPS_MEDIAN	TOTAL_GAPS_STD	TOTAL_GAPS_CV	GAPS_2_PROC_DAYS_AVG	GAPS_2_PROC_DAYS_MEDIAN	GAPS_2_PROC_DAYS_STD	GAPS_2_PROC_DAYS_CV	AVERAGE_TURNOVER_TIME_MINS	MEDIAN_TURNOVER_TIME_MINS	STD_TURNOVER_TIME_MINS	GAPS_3_PLUS_PROC_DAYS_CV	GAPS_3_PROC_DAYS_AVG	GAPS_3_PROC_DAYS_MEDIAN	GAPS_3_PROC_DAYS_STD	GAPS_3_PROC_DAYS_CV	GAPS_4_PROC_DAYS_AVG	GAPS_4_PROC_DAYS_MEDIAN	GAPS_4_PROC_DAYS_STD	GAPS_4_PROC_DAYS_CV	GAPS_5_PROC_DAYS_AVG	GAPS_5_PROC_DAYS_MEDIAN	GAPS_5_PROC_DAYS_STD	GAPS_5_PROC_DAYS_CV	GAPS_6_PROC_DAYS_AVG	GAPS_6_PROC_DAYS_MEDIAN	GAPS_6_PROC_DAYS_STD	GAPS_6_PROC_DAYS_CV	PROC_DAYS_0_PERCENTAGE	PROC_DAYS_1_PERCENTAGE	PROC_DAYS_2_PERCENTAGE	PROC_DAYS_3_PERCENTAGE	PROC_DAYS_4_PERCENTAGE	PROC_DAYS_5_PERCENTAGE	PROC_DAYS_6_PERCENTAGE	PROC_DAYS_7_PERCENTAGE	PROC_DAYS_8_PERCENTAGE	PROC_DAYS_9_PERCENTAGE	PROC_DAYS_10_PERCENTAGE	PROC_DAYS_11_PERCENTAGE	PLUS_PROC_DAYS_3_PLUS_PERCENTAGE	MULTIPLE_CASE_DAYS	NONZERO_CASE_DAYS	AVERAGE_START_TIME	AVERAGE_END_TIME	PERCENTILE_RANK	CSM_NAME	CSD_NAME	CVP_NAME	CSR_NAME	GENESIS_TRAINER	DWLOAD_TS
-- prd	SQ0082	20251	Harris Methodist Fort Worth	15886	001o000000iySjpAAE	da Vinci 5	2024-05-07	F	System		United States		45	61	TRUE	6.566444444	3.871637635	0.8459502243	3.134444444	2.09921041	5.267111111	2.903561305	0.42325	0.2424180971	16.93	9.696723885		1.147519717	0.2086237885	0	61	16	14	17	14	0	0	0	0	0	0	0	0	14	0.866667	0.777778	0.355556	0.737705	126.565	111	62.882	0.497	144.688	137	65.139	0.45	119.036	110	60.882	0.511	119.036	110	60.882	0.511													0.262295082	0.2295081967	0.2786885246	0.2295081967									0.2295081967	31	45	1900-01-01 09:05:50.000	1900-01-01 15:07:49.000	79.04040404	Ryan Kallenberg	Tyler Price	Shelton Sykes	Chris Naughton	Kate Lederer	
-- sbx	SQ0082	20251	Harris Methodist Fort Worth	15886	001o000000iySjpAAE	da Vinci 5	2024-05-07	F	System		United States		45	61	TRUE	6.566444444	3.871637635	0.8459502243	3.134444444	2.09921041	5.267111111	2.903561305	0.42325	0.2424180971	16.93	9.696723885		2.141694075	0.7450615286	0	61	16	14	17	14	0	0	0	0	0	0	0	0	14	0.866667	0.777778	0.355556	0.737705	126.17	110	62.267	0.494	142.529	132	63.781	0.447	119.036	110	60.882	0.511	119.036	110	60.882	0.511													0.262295	0.229508	0.278689	0.229508									0.229508	31	45	1900-01-01 09:05:50.000	1900-01-01 15:07:49.000	79.036331844	Ryan Kallenberg	Tyler Price	Shelton Sykes	Chris Naughton	Kate Lederer			

-- First, let's check record counts by quarter to understand the data distribution

-- Check Q1 2025 record counts in both tables
select 'BACKUP_QTR' as source, qtr, count(*) as record_count
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR
where qtr = '20251'
group by qtr
UNION ALL
select 'CURRENT' as source, qtr, count(*) as record_count
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
where qtr = '20251'
group by qtr;

-- results

-- SOURCE	QTR	RECORD_COUNT
-- BACKUP_QTR	20251	7041
-- CURRENT	20251	7043

select 'BACKUP_QTR' as source, qtr, count(*) as record_count
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR
where qtr = '20252'
group by qtr
UNION ALL
select 'CURRENT' as source, qtr, count(*) as record_count
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
where qtr = '20252'
group by qtr;

-- results

-- SOURCE	QTR	RECORD_COUNT
-- BACKUP_QTR	20251	7041
-- CURRENT	20251	7043

-- Check for specific Q1 2025 variance example
select 'sbx' source, account_id, system_serial_number, qtr,
       total_operating_hours_mean, total_operating_hours_std,
       proc_days_0, proc_days_1, proc_days_2,
       total_gaps_avg, total_gaps_std, average_turnover_time_mins
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
where account_id = '10639' and system_serial_number = 'SK6779' and qtr = '20251'
UNION ALL
select 'prd' source, account_id, system_serial_number, qtr,
       total_operating_hours_mean, total_operating_hours_std,
       proc_days_0, proc_days_1, proc_days_2,
       total_gaps_avg, total_gaps_std, average_turnover_time_mins
from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR
where account_id = '10639' and system_serial_number = 'SK6779' and qtr = '20251';

-- Results

-- SOURCE	ACCOUNT_ID	SYSTEM_SERIAL_NUMBER	QTR	TOTAL_OPERATING_HOURS_MEAN	TOTAL_OPERATING_HOURS_STD	PROC_DAYS_0	PROC_DAYS_1	PROC_DAYS_2	TOTAL_GAPS_AVG	TOTAL_GAPS_STD	AVERAGE_TURNOVER_TIME_MINS
-- sbx	10639	SK6779	20251	6.*********	3.********	27	16	10	99.844	50.402	83.318
-- prd	10639	SK6779	20251	6.*********	3.********	27	16	10	99.452	51.161	81.952

-- DETAILED COLUMN-BY-COLUMN COMPARISON FOR DEBUGGING
-- This will help identify which specific columns are causing the variance
with prd_data as (
  select account_id, system_serial_number, qtr,
         total_operating_hours_mean, total_operating_hours_std,
         percentage_system_used_weekly_avg, percentage_system_used_weekly_std,
         total_gaps_avg, total_gaps_std, average_turnover_time_mins,
         proc_days_0, proc_days_1, proc_days_2, proc_days_3
  from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR
  where account_id = '10639' and system_serial_number = 'SK6779' and qtr = '20251'
),
sbx_data as (
  select account_id, system_serial_number, qtr,
         total_operating_hours_mean, total_operating_hours_std,
         percentage_system_used_weekly_avg, percentage_system_used_weekly_std,
         total_gaps_avg, total_gaps_std, average_turnover_time_mins,
         proc_days_0, proc_days_1, proc_days_2, proc_days_3
  from EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS
  where account_id = '10639' and system_serial_number = 'SK6779' and qtr = '20251'
)
select
  'TOTAL_OPERATING_HOURS_MEAN' as column_name,
  p.total_operating_hours_mean as prd_value,
  s.total_operating_hours_mean as sbx_value,
  (p.total_operating_hours_mean - s.total_operating_hours_mean) as variance
from prd_data p, sbx_data s
UNION ALL
select
  'TOTAL_OPERATING_HOURS_STD' as column_name,
  p.total_operating_hours_std as prd_value,
  s.total_operating_hours_std as sbx_value,
  (p.total_operating_hours_std - s.total_operating_hours_std) as variance
from prd_data p, sbx_data s
UNION ALL
select
  'PERCENTAGE_SYSTEM_USED_WEEKLY_AVG' as column_name,
  p.percentage_system_used_weekly_avg as prd_value,
  s.percentage_system_used_weekly_avg as sbx_value,
  (p.percentage_system_used_weekly_avg - s.percentage_system_used_weekly_avg) as variance
from prd_data p, sbx_data s
UNION ALL
select
  'TOTAL_GAPS_AVG' as column_name,
  p.total_gaps_avg as prd_value,
  s.total_gaps_avg as sbx_value,
  (p.total_gaps_avg - s.total_gaps_avg) as variance
from prd_data p, sbx_data s
where abs(p.total_gaps_avg - s.total_gaps_avg) > 0.01
order by abs(variance) desc;


-- Results


-- COLUMN_NAME	PRD_VALUE	SBX_VALUE	VARIANCE
-- TOTAL_GAPS_AVG	99.452	99.844	-0.392
-- TOTAL_OPERATING_HOURS_STD	3.********	3.********	0
-- TOTAL_OPERATING_HOURS_MEAN	6.*********	6.*********	0
-- PERCENTAGE_SYSTEM_USED_WEEKLY_AVG	0.**********	0.**********	0

-- STEP 1: Check Operating Hours Metrics (most likely culprits)
WITH prd_data AS (
    SELECT account_id, system_serial_number, qtr,
           total_operating_hours_mean, total_operating_hours_std,
           utilization_day_mean, afternoon_hour_on_or_day_mean, afternoon_hour_on_or_day_std,
           or_duration_per_day_mean, or_duration_per_day_std
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
    WHERE date(dwload_ts) IS NULL AND qtr = '20252'
),
sbx_data AS (
    SELECT account_id, system_serial_number, qtr,
           total_operating_hours_mean, total_operating_hours_std,
           utilization_day_mean, afternoon_hour_on_or_day_mean, afternoon_hour_on_or_day_std,
           or_duration_per_day_mean, or_duration_per_day_std
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
    WHERE qtr = '20252'
)
SELECT p.account_id, p.system_serial_number,
       ABS(NVL(p.total_operating_hours_mean,0) - NVL(s.total_operating_hours_mean,0)) as var_op_hours_mean,
       ABS(NVL(p.total_operating_hours_std,0) - NVL(s.total_operating_hours_std,0)) as var_op_hours_std,
       ABS(NVL(p.utilization_day_mean,0) - NVL(s.utilization_day_mean,0)) as var_utilization,
       ABS(NVL(p.afternoon_hour_on_or_day_mean,0) - NVL(s.afternoon_hour_on_or_day_mean,0)) as var_afternoon_mean,
       ABS(NVL(p.afternoon_hour_on_or_day_std,0) - NVL(s.afternoon_hour_on_or_day_std,0)) as var_afternoon_std,
       ABS(NVL(p.or_duration_per_day_mean,0) - NVL(s.or_duration_per_day_mean,0)) as var_or_duration_mean,
       ABS(NVL(p.or_duration_per_day_std,0) - NVL(s.or_duration_per_day_std,0)) as var_or_duration_std
FROM prd_data p
JOIN sbx_data s ON p.account_id = s.account_id 
    AND p.system_serial_number = s.system_serial_number 
    AND p.qtr = s.qtr
WHERE (ABS(NVL(p.total_operating_hours_mean,0) - NVL(s.total_operating_hours_mean,0)) > 0.01
    OR ABS(NVL(p.total_operating_hours_std,0) - NVL(s.total_operating_hours_std,0)) > 0.01
    OR ABS(NVL(p.afternoon_hour_on_or_day_std,0) - NVL(s.afternoon_hour_on_or_day_std,0)) > 0.01
    OR ABS(NVL(p.or_duration_per_day_std,0) - NVL(s.or_duration_per_day_std,0)) > 0.01)
ORDER BY (var_op_hours_mean + var_op_hours_std + var_afternoon_std + var_or_duration_std) DESC
LIMIT 20;

-- results

-- o rows for both q1 and q2 

-- STEP 2: Check Gap/Turnover Metrics
WITH prd_data AS (
    SELECT account_id, system_serial_number, qtr,
           total_gaps_avg, total_gaps_std, average_turnover_time_mins, std_turnover_time_mins,
           gaps_2_proc_days_std, gaps_3_proc_days_std, gaps_4_proc_days_std
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
    WHERE date(dwload_ts) IS NULL AND qtr = '20252'
),
sbx_data AS (
    SELECT account_id, system_serial_number, qtr,
           total_gaps_avg, total_gaps_std, average_turnover_time_mins, std_turnover_time_mins,
           gaps_2_proc_days_std, gaps_3_proc_days_std, gaps_4_proc_days_std
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
    WHERE qtr = '20252'
)
SELECT p.account_id, p.system_serial_number,
       ABS(NVL(p.total_gaps_avg,0) - NVL(s.total_gaps_avg,0)) as var_gaps_avg,
       ABS(NVL(p.total_gaps_std,0) - NVL(s.total_gaps_std,0)) as var_gaps_std,
       ABS(NVL(p.average_turnover_time_mins,0) - NVL(s.average_turnover_time_mins,0)) as var_turnover_avg,
       ABS(NVL(p.std_turnover_time_mins,0) - NVL(s.std_turnover_time_mins,0)) as var_turnover_std,
       ABS(NVL(p.gaps_2_proc_days_std,0) - NVL(s.gaps_2_proc_days_std,0)) as var_gaps_2_std,
       ABS(NVL(p.gaps_3_proc_days_std,0) - NVL(s.gaps_3_proc_days_std,0)) as var_gaps_3_std
FROM prd_data p
JOIN sbx_data s ON p.account_id = s.account_id 
    AND p.system_serial_number = s.system_serial_number 
    AND p.qtr = s.qtr
WHERE (ABS(NVL(p.total_gaps_std,0) - NVL(s.total_gaps_std,0)) > 0.01
    OR ABS(NVL(p.std_turnover_time_mins,0) - NVL(s.std_turnover_time_mins,0)) > 0.01
    OR ABS(NVL(p.gaps_2_proc_days_std,0) - NVL(s.gaps_2_proc_days_std,0)) > 0.01)
ORDER BY (var_gaps_std + var_turnover_std + var_gaps_2_std + var_gaps_3_std) DESC
LIMIT 20;

-- results got only 4 rows q1 

-- ACCOUNT_ID	SYSTEM_SERIAL_NUMBER	VAR_GAPS_AVG	VAR_GAPS_STD	VAR_TURNOVER_AVG	VAR_TURNOVER_STD	VAR_GAPS_2_STD	VAR_GAPS_3_STD
-- 15886	SQ0082	0.395	0.615	0	0	1.358	0
-- 10639	SK6779	0.392	0.759	1.366	0.243	0	0
-- 119729	SK8026	0.271	0.069	0.556	0.241	0	0.072
-- 11579	SK1081	0.194	0.161	0.116	0.111	0	0

-- results for q2

-- ACCOUNT_ID	SYSTEM_SERIAL_NUMBER	VAR_GAPS_AVG	VAR_GAPS_STD	VAR_TURNOVER_AVG	VAR_TURNOVER_STD	VAR_GAPS_2_STD	VAR_GAPS_3_STD
-- 20335	SK2921	0.735	0.927	0	0	3.745	0
-- 14833	SK6420	0.441	0.813	1.045	1.837	0	0.797
-- 13413	SK1678	0.762	0.488	0.6	1.223	0	1.223
-- 117778	SK0075	0.245	1.535	0	0	0	0
-- 12480	SK0459	0.072	0.319	0.024	0.245	0	0.787


-- Alternative: Check a sample record to see available columns
SELECT * 
FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL 
WHERE account_id = '12143' 
  AND system_serial_number = 'SQ0603' 
LIMIT 1;

-- result

-- ACCOUNT_ID	ACCOUNT_NAME	SYSTEM_SERIAL_NUMBER	PROCEDURE_NUMBER	PROCEDURE_DATE_LOCAL	START_TIME_LOCAL	PROCEDURE_DURATION	SURGEON_NAME	SURGEON_ID	SURGEON_SPECIALITY	SYSTEM_LOCATION_TYPE	BUSINESS_CATEGORY	PROCEDURE_NAME	SUBJECT	CAL_YEAR_QTR	COUNTRY	START_TIME_WITH_DATE	HOUR_OF_PROCEDURE_START	END_TIME_WITH_DATE	HOUR_OF_PROCEDURE_END	BUSINESS_DAY_DATE	SYSTEM_NAME	PROCEDURE_COUNT	PROCEDURE_DAYS	DWLOAD_TS	DAY_OF_WEEK
-- 12143	Ascension St. Vincent Hospital - Indianapolis	SQ0603	C25208430	2025-03-31 00:00:00.000	09:01 AM	8760	Nakul Valsangkar	304211	THORACIC		Thoracic	Hiatal Hernia - Paraesophageal	Foregut	20251	United States	2025-03-31 09:01:00.000	9	2025-03-31 11:27:00.000	11	2025-03-31	SQ0603	1	1		Monday


SELECT 
    'PROCEDURE_COUNT_CHECK' as check_type,
    COUNT(*) as total_procedures,
    COUNT(DISTINCT procedure_date_local) as distinct_days,
    MIN(procedure_date_local) as first_date,
    MAX(procedure_date_local) as last_date
FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL p
JOIN EDW.MASTER.VW_SFDC_ISICALENDAR cal ON p.procedure_date_local = cal.calday
WHERE p.account_id = '10639' 
  AND p.system_serial_number = 'SK6779' 
  AND cal.calyearqtr = '20251'
ORDER BY check_type;

-- result

-- CHECK_TYPE	TOTAL_PROCEDURES	DISTINCT_DAYS	FIRST_DATE	LAST_DATE
-- PROCEDURE_COUNT_CHECK	5037	37	2025-01-06 00:00:00.000	2025-03-31 00:00:00.000


 -- 15008	SQ0301
SELECT 
    'PROCEDURE_COUNT_CHECK' as check_type,
    COUNT(*) as total_procedures,
    COUNT(DISTINCT procedure_date_local) as distinct_days,
    MIN(procedure_date_local) as first_date,
    MAX(procedure_date_local) as last_date
FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL p
JOIN EDW.MASTER.VW_SFDC_ISICALENDAR cal ON p.procedure_date_local = cal.calday
WHERE p.account_id = '15008' 
  AND p.system_serial_number = 'SQ0301' 
  AND cal.calyearqtr = '20252'
ORDER BY check_type;

-- result q2

-- CHECK_TYPE	TOTAL_PROCEDURES	DISTINCT_DAYS	FIRST_DATE	LAST_DATE
-- PROCEDURE_COUNT_CHECK	3723	27	2025-05-05 00:00:00.000	2025-06-30 00:00:00.000



-- Simulate the gap calculation to see what's different
WITH procedure_data AS (
    SELECT 
        account_id, system_serial_number, procedure_date_local,
        start_time_with_date, end_time_with_date,
        ROW_NUMBER() OVER (PARTITION BY account_id, system_serial_number ORDER BY start_time_with_date) as proc_seq
    FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL p
    JOIN EDW.MASTER.VW_SFDC_ISICALENDAR cal ON p.procedure_date_local = cal.calday
    WHERE p.account_id = '10639' 
      AND p.system_serial_number = 'SK6779' 
      AND cal.calyearqtr = '20251'
),
gaps_calculated AS (
    SELECT *,
        LEAD(start_time_with_date) OVER (ORDER BY start_time_with_date) as next_start_time,
        LEAD(procedure_date_local) OVER (ORDER BY start_time_with_date) as next_procedure_date,
        CASE 
            WHEN procedure_date_local = LEAD(procedure_date_local) OVER (ORDER BY start_time_with_date)
            THEN DATEDIFF('minute', end_time_with_date, LEAD(start_time_with_date) OVER (ORDER BY start_time_with_date))
            ELSE NULL 
        END as gap_minutes
    FROM procedure_data
)
SELECT 
    COUNT(*) as total_gaps,
    COUNT(gap_minutes) as valid_gaps,
    AVG(gap_minutes) as avg_gap,
    STDDEV_POP(gap_minutes) as std_gap
FROM gaps_calculated
WHERE gap_minutes > 0;

-- results

-- TOTAL_GAPS	VALID_GAPS	AVG_GAP	STD_GAP
-- 32	32	99.843750	50.*********

-- Simulate the gap calculation to see what's different
WITH procedure_data AS (
    SELECT 
        account_id, system_serial_number, procedure_date_local,
        start_time_with_date, end_time_with_date,
        ROW_NUMBER() OVER (PARTITION BY account_id, system_serial_number ORDER BY start_time_with_date) as proc_seq
    FROM EDWSBX.TRAINING.GENESIS_PROCEDURE_DATA_HISTORICAL p
    JOIN EDW.MASTER.VW_SFDC_ISICALENDAR cal ON p.procedure_date_local = cal.calday
    WHERE p.account_id = '15008' 
      AND p.system_serial_number = 'SQ0301' 
      AND cal.calyearqtr = '20252'
),
gaps_calculated AS (
    SELECT *,
        LEAD(start_time_with_date) OVER (ORDER BY start_time_with_date) as next_start_time,
        LEAD(procedure_date_local) OVER (ORDER BY start_time_with_date) as next_procedure_date,
        CASE 
            WHEN procedure_date_local = LEAD(procedure_date_local) OVER (ORDER BY start_time_with_date)
            THEN DATEDIFF('minute', end_time_with_date, LEAD(start_time_with_date) OVER (ORDER BY start_time_with_date))
            ELSE NULL 
        END as gap_minutes
    FROM procedure_data
)
SELECT 
    COUNT(*) as total_gaps,
    COUNT(gap_minutes) as valid_gaps,
    AVG(gap_minutes) as avg_gap,
    STDDEV_POP(gap_minutes) as std_gap
FROM gaps_calculated
WHERE gap_minutes > 0;

-- result

-- TOTAL_GAPS	VALID_GAPS	AVG_GAP	STD_GAP
-- 24	24	145.416667	31.*********

-- STEP 4: Check Weekly Features (potential major contributor)
WITH prd_data AS (
    SELECT account_id, system_serial_number, qtr,
           percentage_system_used_weekly_avg, percentage_system_used_weekly_std,
           hours_system_used_weekly_avg, hours_system_used_weekly_std,
           weekly_system_used_std_median
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
    WHERE date(dwload_ts) IS NULL AND qtr = '20252'
),
sbx_data AS (
    SELECT account_id, system_serial_number, qtr,
           percentage_system_used_weekly_avg, percentage_system_used_weekly_std,
           hours_system_used_weekly_avg, hours_system_used_weekly_std,
           weekly_system_used_std_median
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
    WHERE qtr = '20252'
)
SELECT p.account_id, p.system_serial_number,
       ABS(NVL(p.percentage_system_used_weekly_avg,0) - NVL(s.percentage_system_used_weekly_avg,0)) as var_weekly_pct_avg,
       ABS(NVL(p.percentage_system_used_weekly_std,0) - NVL(s.percentage_system_used_weekly_std,0)) as var_weekly_pct_std,
       ABS(NVL(p.hours_system_used_weekly_avg,0) - NVL(s.hours_system_used_weekly_avg,0)) as var_weekly_hrs_avg,
       ABS(NVL(p.hours_system_used_weekly_std,0) - NVL(s.hours_system_used_weekly_std,0)) as var_weekly_hrs_std,
       ABS(NVL(p.weekly_system_used_std_median,0) - NVL(s.weekly_system_used_std_median,0)) as var_weekly_median
FROM prd_data p
JOIN sbx_data s ON p.account_id = s.account_id 
    AND p.system_serial_number = s.system_serial_number 
    AND p.qtr = s.qtr
WHERE (ABS(NVL(p.percentage_system_used_weekly_std,0) - NVL(s.percentage_system_used_weekly_std,0)) > 0.01
    OR ABS(NVL(p.hours_system_used_weekly_std,0) - NVL(s.hours_system_used_weekly_std,0)) > 0.01
    OR ABS(NVL(p.weekly_system_used_std_median,0) - NVL(s.weekly_system_used_std_median,0)) > 0.01)
ORDER BY (var_weekly_pct_std + var_weekly_hrs_std + var_weekly_median) DESC
LIMIT 20;

-- results for q1 and q2 - 0 rows


-- STEP 5: Check Procedure Day Counts (could be major) Q1
WITH prd_data AS (
    SELECT account_id, system_serial_number, qtr,
           proc_days_0, proc_days_1, proc_days_2, proc_days_3, proc_days_4, proc_days_5,
           plus_proc_days_3_plus_percentage
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
    WHERE date(dwload_ts) IS NULL AND qtr = '20252'
),
sbx_data AS (
    SELECT account_id, system_serial_number, qtr,
           proc_days_0, proc_days_1, proc_days_2, proc_days_3, proc_days_4, proc_days_5,
           plus_proc_days_3_plus_percentage
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
    WHERE qtr = '20252'
)
SELECT p.account_id, p.system_serial_number,
       ABS(NVL(p.proc_days_0,0) - NVL(s.proc_days_0,0)) as var_proc_days_0,
       ABS(NVL(p.proc_days_1,0) - NVL(s.proc_days_1,0)) as var_proc_days_1,
       ABS(NVL(p.proc_days_2,0) - NVL(s.proc_days_2,0)) as var_proc_days_2,
       ABS(NVL(p.proc_days_3,0) - NVL(s.proc_days_3,0)) as var_proc_days_3,
       ABS(NVL(p.plus_proc_days_3_plus_percentage,0) - NVL(s.plus_proc_days_3_plus_percentage,0)) as var_3plus_pct
FROM prd_data p
JOIN sbx_data s ON p.account_id = s.account_id 
    AND p.system_serial_number = s.system_serial_number 
    AND p.qtr = s.qtr
WHERE (ABS(NVL(p.proc_days_0,0) - NVL(s.proc_days_0,0)) > 0
    OR ABS(NVL(p.proc_days_1,0) - NVL(s.proc_days_1,0)) > 0
    OR ABS(NVL(p.proc_days_2,0) - NVL(s.proc_days_2,0)) > 0)
ORDER BY (var_proc_days_0 + var_proc_days_1 + var_proc_days_2 + var_proc_days_3) DESC
LIMIT 20;

-- results 0 rows for q1 and q2

-- Check Procedure Day Counts (could be major) Q2
WITH prd_data AS (
    SELECT account_id, system_serial_number, qtr,
           proc_days_0, proc_days_1, proc_days_2, proc_days_3, proc_days_4, proc_days_5,
           plus_proc_days_3_plus_percentage
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS_BKUP_QTR 
    WHERE date(dwload_ts) IS NULL AND qtr = '20252'
),
sbx_data AS (
    SELECT account_id, system_serial_number, qtr,
           proc_days_0, proc_days_1, proc_days_2, proc_days_3, proc_days_4, proc_days_5,
           plus_proc_days_3_plus_percentage
    FROM EDWSBX.TRAINING.GENESIS_SYSTEM_LEVEL_HISTORICAL_CHUNKS 
    WHERE qtr = '20252'
)
SELECT p.account_id, p.system_serial_number,
       ABS(NVL(p.proc_days_0,0) - NVL(s.proc_days_0,0)) as var_proc_days_0,
       ABS(NVL(p.proc_days_1,0) - NVL(s.proc_days_1,0)) as var_proc_days_1,
       ABS(NVL(p.proc_days_2,0) - NVL(s.proc_days_2,0)) as var_proc_days_2,
       ABS(NVL(p.proc_days_3,0) - NVL(s.proc_days_3,0)) as var_proc_days_3,
       ABS(NVL(p.plus_proc_days_3_plus_percentage,0) - NVL(s.plus_proc_days_3_plus_percentage,0)) as var_3plus_pct
FROM prd_data p
JOIN sbx_data s ON p.account_id = s.account_id 
    AND p.system_serial_number = s.system_serial_number 
    AND p.qtr = s.qtr
WHERE (ABS(NVL(p.proc_days_0,0) - NVL(s.proc_days_0,0)) > 0
    OR ABS(NVL(p.proc_days_1,0) - NVL(s.proc_days_1,0)) > 0
    OR ABS(NVL(p.proc_days_2,0) - NVL(s.proc_days_2,0)) > 0)
ORDER BY (var_proc_days_0 + var_proc_days_1 + var_proc_days_2 + var_proc_days_3) DESC
LIMIT 20;

-- results for q1  and q2 - 0 rows

